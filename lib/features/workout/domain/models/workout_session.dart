import 'package:freezed_annotation/freezed_annotation.dart';

part 'workout_session.freezed.dart';
part 'workout_session.g.dart';

@freezed
class WorkoutSession with _$WorkoutSession {
  const factory WorkoutSession({
    required String id,
    required String name,
    required String description,
    required int estimatedDuration, // in minutes
    required int estimatedCalories,
    required int totalSets,
    required List<WorkoutExercise> exercises,
    String? backgroundImageUrl,
    @Default(WorkoutStatus.ready) WorkoutStatus status,
    DateTime? startedAt,
    DateTime? completedAt,
    @Default(0) int currentExerciseIndex,
    @Default(0) int currentSetIndex,
    @Default({}) Map<String, dynamic> sessionData,
  }) = _WorkoutSession;

  factory WorkoutSession.fromJson(Map<String, dynamic> json) =>
      _$WorkoutSessionFromJson(json);
}

@freezed
class WorkoutExercise with _$WorkoutExercise {
  const factory WorkoutExercise({
    required String id,
    required String name,
    required String description,
    required int sets,
    required List<int> targetReps,
    required List<double> targetWeights, // in lbs (imperial)
    List<int>? completedReps,
    List<double>? completedWeights,
    String? videoUrl,
    String? thumbnailUrl,
    String? instructions,
    @Default(60) int restInterval, // in seconds
    @Default(false) bool isCompleted,
    @Default(0) int completedSets,
  }) = _WorkoutExercise;

  factory WorkoutExercise.fromJson(Map<String, dynamic> json) =>
      _$WorkoutExerciseFromJson(json);
}

enum WorkoutStatus {
  ready,
  inProgress,
  paused,
  completed,
  cancelled,
}

/// Extension for WorkoutSession to provide formatted values and utilities
extension WorkoutSessionExtension on WorkoutSession {
  /// Get duration text in imperial format
  String get durationText => '$estimatedDuration min';

  /// Get calories text in imperial format
  String get caloriesText => '$estimatedCalories kcal';

  /// Get sets text with proper pluralization
  String get setsText {
    if (totalSets == 1) return '1 set';
    return '$totalSets sets';
  }

  /// Get exercises text with proper pluralization
  String get exercisesText {
    if (exercises.length == 1) return '1 exercise';
    return '${exercises.length} exercises';
  }

  /// Get completion percentage
  double get completionPercentage {
    if (exercises.isEmpty) return 0.0;
    final completedExercises = exercises.where((e) => e.isCompleted).length;
    return completedExercises / exercises.length;
  }

  /// Get current exercise
  WorkoutExercise? get currentExercise {
    if (currentExerciseIndex >= exercises.length) return null;
    return exercises[currentExerciseIndex];
  }

  /// Get next exercise to perform
  WorkoutExercise? get nextExercise {
    final nextIndex = currentExerciseIndex + 1;
    if (nextIndex >= exercises.length) return null;
    return exercises[nextIndex];
  }

  /// Check if workout can be started
  bool get canStart => status == WorkoutStatus.ready;

  /// Check if workout can be resumed
  bool get canResume => status == WorkoutStatus.paused;

  /// Check if workout is active
  bool get isActive => status == WorkoutStatus.inProgress;

  /// Check if workout is completed
  bool get isCompleted => status == WorkoutStatus.completed;

  /// Get status text for display
  String get statusText {
    switch (status) {
      case WorkoutStatus.ready:
        return 'Ready to Start';
      case WorkoutStatus.inProgress:
        return 'In Progress';
      case WorkoutStatus.paused:
        return 'Paused';
      case WorkoutStatus.completed:
        return 'Completed';
      case WorkoutStatus.cancelled:
        return 'Cancelled';
    }
  }

  /// Get motivational start message
  String get startMessage {
    final hour = DateTime.now().hour;
    if (hour < 12) {
      return 'Start your day strong! 💪';
    } else if (hour < 17) {
      return 'Power through your workout! 🔥';
    } else {
      return 'Finish strong today! ⚡';
    }
  }

  /// Get elapsed time in minutes
  int get elapsedMinutes {
    if (startedAt == null) return 0;
    final now = completedAt ?? DateTime.now();
    return now.difference(startedAt!).inMinutes;
  }

  /// Get remaining exercises count
  int get remainingExercises {
    return exercises.where((e) => !e.isCompleted).length;
  }

  /// Get total completed sets
  int get totalCompletedSets {
    return exercises.fold<int>(
      0,
      (sum, exercise) => sum + exercise.completedSets,
    );
  }
}

/// Extension for WorkoutExercise to provide formatted values and utilities
extension WorkoutExerciseExtension on WorkoutExercise {
  /// Get sets text with proper pluralization
  String get setsText {
    if (sets == 1) return '1 set';
    return '$sets sets';
  }

  /// Get completion percentage for this exercise
  double get completionPercentage {
    if (sets == 0) return 0.0;
    return completedSets / sets;
  }

  /// Get remaining sets
  int get remainingSets => sets - completedSets;

  /// Get current set number (1-based)
  int get currentSetNumber => completedSets + 1;

  /// Check if exercise has next set
  bool get hasNextSet => completedSets < sets;

  /// Get target reps for current set
  int get currentTargetReps {
    if (targetReps.isEmpty) return 10;
    final index = completedSets.clamp(0, targetReps.length - 1);
    return targetReps[index];
  }

  /// Get target weight for current set
  double get currentTargetWeight {
    if (targetWeights.isEmpty) return 0.0;
    final index = completedSets.clamp(0, targetWeights.length - 1);
    return targetWeights[index];
  }

  /// Get formatted weight text
  String get currentWeightText {
    final weight = currentTargetWeight;
    if (weight == 0.0) return 'Bodyweight';
    return '${weight.toInt()} lbs';
  }

  /// Get rest interval text
  String get restIntervalText {
    if (restInterval < 60) {
      return '${restInterval}s rest';
    } else {
      final minutes = restInterval ~/ 60;
      final seconds = restInterval % 60;
      if (seconds == 0) {
        return '${minutes}m rest';
      } else {
        return '${minutes}m ${seconds}s rest';
      }
    }
  }

  /// Check if this is the last set
  bool get isLastSet => currentSetNumber >= sets;

  /// Get progress text for display
  String get progressText => 'Set $currentSetNumber of $sets';
}
