import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/theme/color_palette.dart';
import '../../../../core/theme/typography.dart';
import '../../../../core/animations/animation_utils.dart';
import '../../../../shared/widgets/glass_morphism_card.dart';
import '../../domain/models/workout_session.dart';
import '../widgets/workout_timer.dart';
import '../widgets/exercise_video_player.dart';
import '../widgets/set_counter_widget.dart';
import '../widgets/ai_coach_avatar.dart';
import '../widgets/exercise_progress_bar.dart';

class ActiveWorkoutScreen extends ConsumerStatefulWidget {
  final WorkoutSession workout;

  const ActiveWorkoutScreen({
    super.key,
    required this.workout,
  });

  @override
  ConsumerState<ActiveWorkoutScreen> createState() => _ActiveWorkoutScreenState();
}

class _ActiveWorkoutScreenState extends ConsumerState<ActiveWorkoutScreen>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  
  Timer? _workoutTimer;
  Duration _elapsedTime = Duration.zero;
  int _currentExerciseIndex = 0;
  int _currentSetIndex = 0;
  bool _isResting = false;
  
  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _startAnimations();
    _startWorkoutTimer();
  }

  void _setupAnimations() {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _slideController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(
      CurvedAnimation(
        parent: _fadeController,
        curve: Curves.easeOut,
      ),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(
        parent: _slideController,
        curve: FitnessAnimationCurves.springCurve,
      ),
    );
  }

  void _startAnimations() {
    _fadeController.forward();
    Future.delayed(const Duration(milliseconds: 200), () {
      _slideController.forward();
    });
  }

  void _startWorkoutTimer() {
    _workoutTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (mounted && !_isResting) {
        setState(() {
          _elapsedTime = Duration(seconds: _elapsedTime.inSeconds + 1);
        });
      }
    });
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _slideController.dispose();
    _workoutTimer?.cancel();
    super.dispose();
  }

  WorkoutExercise get _currentExercise => widget.workout.exercises[_currentExerciseIndex];
  double get _overallProgress => (_currentExerciseIndex + 1) / widget.workout.exercises.length;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: AppColorPalette.darkBackground,
      body: SafeArea(
        child: AnimatedBuilder(
          animation: _fadeController,
          builder: (context, child) {
            return FadeTransition(
              opacity: _fadeAnimation,
              child: SlideTransition(
                position: _slideAnimation,
                child: Column(
                  children: [
                    _buildTopSection(context, theme),
                    Expanded(
                      child: _buildMainContent(context, theme),
                    ),
                    _buildBottomNavigation(context, theme),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildTopSection(BuildContext context, ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          // Header with timer and progress
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // Back button
              Container(
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: IconButton(
                  onPressed: () => _showPauseDialog(context),
                  icon: const Icon(Icons.pause, color: Colors.white),
                ),
              ),
              
              // Timer and progress
              Column(
                children: [
                  WorkoutTimer(
                    elapsedTime: _elapsedTime,
                    textColor: Colors.white,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '${(_overallProgress * 100).toInt()}% Complete',
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.8),
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
              
              // Menu button
              Container(
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: IconButton(
                  onPressed: () => _showWorkoutMenu(context),
                  icon: const Icon(Icons.more_vert, color: Colors.white),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Exercise progress bar
          ExerciseProgressBar(
            exercises: widget.workout.exercises,
            currentIndex: _currentExerciseIndex,
          ),
        ],
      ),
    );
  }

  Widget _buildMainContent(BuildContext context, ThemeData theme) {
    return Column(
      children: [
        // Video player section
        Expanded(
          flex: 3,
          child: Container(
            margin: const EdgeInsets.symmetric(horizontal: 20),
            child: ExerciseVideoPlayer(
              exercise: _currentExercise,
              onTap: _toggleVideoPlayback,
            ),
          ),
        ),
        
        const SizedBox(height: 20),
        
        // Exercise info and controls
        Expanded(
          flex: 2,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Column(
              children: [
                // Exercise name and set info
                Text(
                  _currentExercise.name,
                  style: AppTypography.displayNumbers(
                    fontSize: 24,
                    color: Colors.white,
                    fontWeight: FontWeight.w700,
                  ),
                  textAlign: TextAlign.center,
                ),
                
                const SizedBox(height: 8),
                
                Text(
                  _currentExercise.progressText,
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.8),
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                
                const SizedBox(height: 24),
                
                // Set counter
                SetCounterWidget(
                  currentReps: _currentExercise.currentTargetReps,
                  currentWeight: _currentExercise.currentTargetWeight,
                  onRepsChanged: _updateReps,
                  onWeightChanged: _updateWeight,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildBottomNavigation(BuildContext context, ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          // AI Coach Avatar
          const AICoachAvatar(),
          
          const SizedBox(height: 16),
          
          // Next exercise preview
          if (_currentExerciseIndex < widget.workout.exercises.length - 1)
            _buildNextExercisePreview(theme),
          
          const SizedBox(height: 16),
          
          // Action buttons
          Row(
            children: [
              // Skip set button
              Expanded(
                child: OutlinedButton(
                  onPressed: _skipSet,
                  style: OutlinedButton.styleFrom(
                    foregroundColor: Colors.white,
                    side: const BorderSide(color: Colors.white, width: 1),
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: const Text('Skip Set'),
                ),
              ),
              
              const SizedBox(width: 16),
              
              // Complete set button
              Expanded(
                flex: 2,
                child: ElevatedButton(
                  onPressed: _completeSet,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColorPalette.primaryOrange,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: Text(
                    _currentExercise.isLastSet ? 'Complete Exercise' : 'Complete Set',
                    style: const TextStyle(fontWeight: FontWeight.w600),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildNextExercisePreview(ThemeData theme) {
    final nextExercise = widget.workout.exercises[_currentExerciseIndex + 1];
    
    return GlassMorphismCard(
      padding: const EdgeInsets.all(12),
      child: Row(
        children: [
          Icon(
            Icons.skip_next,
            color: AppColorPalette.primaryOrange,
            size: 20,
          ),
          const SizedBox(width: 8),
          Text(
            'Next: ${nextExercise.name}',
            style: TextStyle(
              color: Colors.white.withOpacity(0.9),
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  void _toggleVideoPlayback() {
    HapticFeedback.lightImpact();
    // TODO: Implement video playback toggle
  }

  void _updateReps(int reps) {
    // TODO: Update current set reps
  }

  void _updateWeight(double weight) {
    // TODO: Update current set weight
  }

  void _skipSet() {
    HapticFeedback.lightImpact();
    // TODO: Skip current set
  }

  void _completeSet() {
    HapticFeedback.mediumImpact();
    
    if (_currentExercise.isLastSet) {
      _completeExercise();
    } else {
      _moveToNextSet();
    }
  }

  void _moveToNextSet() {
    setState(() {
      _currentSetIndex++;
    });
    
    // Start rest timer
    _startRestPeriod();
  }

  void _completeExercise() {
    if (_currentExerciseIndex < widget.workout.exercises.length - 1) {
      setState(() {
        _currentExerciseIndex++;
        _currentSetIndex = 0;
      });
      _startRestPeriod();
    } else {
      _completeWorkout();
    }
  }

  void _startRestPeriod() {
    setState(() {
      _isResting = true;
    });
    
    // TODO: Navigate to rest screen
    Navigator.pushNamed(
      context,
      '/rest-screen',
      arguments: {
        'duration': _currentExercise.restInterval,
        'isLastSet': _currentExercise.isLastSet,
        'nextExercise': _currentExerciseIndex < widget.workout.exercises.length - 1
            ? widget.workout.exercises[_currentExerciseIndex + 1]
            : null,
      },
    ).then((_) {
      setState(() {
        _isResting = false;
      });
    });
  }

  void _completeWorkout() {
    // TODO: Navigate to completion screen
    Navigator.pushReplacementNamed(
      context,
      '/workout-completion',
      arguments: widget.workout,
    );
  }

  void _showPauseDialog(BuildContext context) {
    HapticFeedback.lightImpact();
    // TODO: Show pause dialog
  }

  void _showWorkoutMenu(BuildContext context) {
    HapticFeedback.lightImpact();
    // TODO: Show workout menu
  }
}
