import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../auth/domain/providers/auth_provider.dart';
import '../../../profile/presentation/screens/profile_screen.dart';
import '../../../workout/presentation/screens/workout_list_screen.dart';
import '../../domain/providers/navigation_provider.dart';
import '../../../../core/theme/spacing.dart';
import '../../../../core/theme/color_palette.dart';

class HomeScreen extends ConsumerWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentUser = ref.watch(currentUserProvider);
    final selectedIndex = ref.watch(navigationIndexProvider);

    final screens = [
      const _HomeContent(),
      const WorkoutListScreen(),
      const ProfileScreen(),
    ];

    return Scaffold(
      body: screens[selectedIndex],
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: selectedIndex,
        onTap: (index) {
          ref.read(navigationIndexProvider.notifier).state = index;
        },
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.home),
            label: 'Home',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.fitness_center),
            label: 'Workouts',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.person),
            label: 'Profile',
          ),
        ],
      ),
    );
  }
}

class _HomeContent extends StatelessWidget {
  const _HomeContent();

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'FitTracker',
          style: textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          Container(
            margin: AppSpacing.paddingHorizontalMd,
            decoration: BoxDecoration(
              color: colorScheme.surface,
              borderRadius: AppSpacing.borderRadiusMd,
              border: Border.all(
                color: colorScheme.outline.withOpacity(0.2),
                width: 1,
              ),
            ),
            child: IconButton(
              icon: Icon(
                Icons.notifications_outlined,
                color: colorScheme.onSurface,
              ),
              onPressed: () {},
            ),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: AppSpacing.paddingLg,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Welcome Section
            Container(
              padding: AppSpacing.paddingLg,
              decoration: BoxDecoration(
                gradient: AppColorPalette.primaryGradient,
                borderRadius: AppSpacing.borderRadiusXl,
                boxShadow: [
                  BoxShadow(
                    color: colorScheme.primary.withOpacity(0.3),
                    blurRadius: 20,
                    offset: const Offset(0, 10),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Welcome back!',
                    style: textTheme.headlineMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  AppSpacing.gapVerticalSm,
                  Text(
                    'Ready for your next workout?',
                    style: textTheme.bodyLarge?.copyWith(
                      color: Colors.white.withOpacity(0.9),
                    ),
                  ),
                  AppSpacing.gapVerticalLg,
                  Container(
                    padding: AppSpacing.paddingMd,
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      borderRadius: AppSpacing.borderRadiusMd,
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Icon(
                          Icons.local_fire_department,
                          color: Colors.white,
                          size: AppSpacing.iconSm,
                        ),
                        AppSpacing.gapHorizontalSm,
                        Text(
                          '3 day streak!',
                          style: textTheme.labelLarge?.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            AppSpacing.gapVerticalXl,
            
            // Quick Stats Section
            Text(
              'Quick Stats',
              style: textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            AppSpacing.gapVerticalMd,
            Row(
              children: [
                Expanded(
                  child: Card(
                    child: Padding(
                      padding: AppSpacing.paddingLg,
                      child: Column(
                        children: [
                          Container(
                            padding: AppSpacing.paddingMd,
                            decoration: BoxDecoration(
                              color: colorScheme.primaryContainer,
                              borderRadius: AppSpacing.borderRadiusMd,
                            ),
                            child: Icon(
                              Icons.fitness_center,
                              color: colorScheme.onPrimaryContainer,
                              size: AppSpacing.iconMd,
                            ),
                          ),
                          AppSpacing.gapVerticalMd,
                          Text(
                            '12',
                            style: textTheme.headlineMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            'Workouts',
                            style: textTheme.bodyMedium?.copyWith(
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                AppSpacing.gapHorizontalMd,
                Expanded(
                  child: Card(
                    child: Padding(
                      padding: AppSpacing.paddingLg,
                      child: Column(
                        children: [
                          Container(
                            padding: AppSpacing.paddingMd,
                            decoration: BoxDecoration(
                              color: colorScheme.tertiaryContainer,
                              borderRadius: AppSpacing.borderRadiusMd,
                            ),
                            child: Icon(
                              Icons.timer_outlined,
                              color: colorScheme.onTertiaryContainer,
                              size: AppSpacing.iconMd,
                            ),
                          ),
                          AppSpacing.gapVerticalMd,
                          Text(
                            '45',
                            style: textTheme.headlineMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            'Minutes',
                            style: textTheme.bodyMedium?.copyWith(
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
            AppSpacing.gapVerticalXl,
            
            // Suggested Workout Section
            Text(
              'Suggested Workout',
              style: textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            AppSpacing.gapVerticalMd,
            Card(
              child: Padding(
                padding: AppSpacing.paddingLg,
                child: Row(
                  children: [
                    Container(
                      width: AppSpacing.avatarLg,
                      height: AppSpacing.avatarLg,
                      decoration: BoxDecoration(
                        gradient: AppColorPalette.primaryGradient,
                        borderRadius: AppSpacing.borderRadiusMd,
                      ),
                      child: Icon(
                        Icons.fitness_center,
                        color: Colors.white,
                        size: AppSpacing.iconLg,
                      ),
                    ),
                    AppSpacing.gapHorizontalMd,
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Upper Body Strength',
                            style: textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          AppSpacing.gapVerticalXs,
                          Text(
                            '30 minutes • Intermediate',
                            style: textTheme.bodyMedium,
                          ),
                          AppSpacing.gapVerticalSm,
                          Row(
                            children: [
                              Icon(
                                Icons.star,
                                size: AppSpacing.iconSm,
                                color: AppColorPalette.warning,
                              ),
                              AppSpacing.gapHorizontalXs,
                              Text(
                                '4.8',
                                style: textTheme.labelMedium?.copyWith(
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    Container(
                      padding: AppSpacing.paddingMd,
                      decoration: BoxDecoration(
                        color: colorScheme.primaryContainer,
                        borderRadius: AppSpacing.borderRadiusSm,
                      ),
                      child: Text(
                        'Start',
                        style: textTheme.labelLarge?.copyWith(
                          color: colorScheme.onPrimaryContainer,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            AppSpacing.gapVerticalLg,
          ],
        ),
      ),
    );
  }
}
