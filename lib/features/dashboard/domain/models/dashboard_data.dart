import 'package:freezed_annotation/freezed_annotation.dart';
import 'user_stats.dart';
import 'today_workout.dart';

part 'dashboard_data.freezed.dart';
part 'dashboard_data.g.dart';

@freezed
class DashboardData with _$DashboardData {
  const factory DashboardData({
    required UserStats userStats,
    TodayWorkout? todayWorkout,
  }) = _DashboardData;

  factory DashboardData.fromJson(Map<String, dynamic> json) =>
      _$DashboardDataFromJson(json);
}
