import 'package:freezed_annotation/freezed_annotation.dart';

part 'today_workout.freezed.dart';
part 'today_workout.g.dart';

@freezed
class TodayWorkout with _$TodayWorkout {
  const factory TodayWorkout({
    required String id,
    required String name,
    required String description,
    required int estimatedDuration, // in minutes
    required int estimatedCalories,
    required int totalSets,
    required List<WorkoutExercise> exercises,
    String? backgroundImageUrl,
    @Default(false) bool isCompleted,
    @Default(false) bool isStarted,
    DateTime? startedAt,
    DateTime? completedAt,
  }) = _TodayWorkout;

  factory TodayWorkout.fromJson(Map<String, dynamic> json) =>
      _$TodayWorkoutFromJson(json);
}

@freezed
class WorkoutExercise with _$WorkoutExercise {
  const factory WorkoutExercise({
    required String id,
    required String name,
    required String description,
    required int sets,
    required List<int> reps,
    required List<double> weights, // in lbs (imperial)
    String? videoUrl,
    String? thumbnailUrl,
    String? instructions,
    @Default(60) int restInterval, // in seconds
    @Default(false) bool isCompleted,
  }) = _WorkoutExercise;

  factory WorkoutExercise.fromJson(Map<String, dynamic> json) =>
      _$WorkoutExerciseFromJson(json);
}

/// Extension for TodayWorkout to provide formatted values and utilities
extension TodayWorkoutExtension on TodayWorkout {
  /// Get duration text in imperial format
  String get durationText => '$estimatedDuration min';

  /// Get calories text in imperial format
  String get caloriesText => '$estimatedCalories kcal';

  /// Get sets text with proper pluralization
  String get setsText {
    if (totalSets == 1) return '1 set';
    return '$totalSets sets';
  }

  /// Get quick stats text for display
  String get quickStatsText => '$durationText • $caloriesText • $setsText';

  /// Get completion percentage
  double get completionPercentage {
    if (exercises.isEmpty) return 0.0;
    final completedExercises = exercises.where((e) => e.isCompleted).length;
    return completedExercises / exercises.length;
  }

  /// Get next exercise to perform
  WorkoutExercise? get nextExercise {
    return exercises.firstWhere(
      (exercise) => !exercise.isCompleted,
      orElse: () => exercises.first,
    );
  }

  /// Check if workout can be started
  bool get canStart => !isCompleted && !isStarted;

  /// Check if workout can be resumed
  bool get canResume => isStarted && !isCompleted;

  /// Get status text for display
  String get statusText {
    if (isCompleted) return 'Completed';
    if (isStarted) return 'In Progress';
    return 'Ready to Start';
  }

  /// Get motivational start message
  String get startMessage {
    final hour = DateTime.now().hour;
    if (hour < 12) {
      return 'Start your day strong!';
    } else if (hour < 17) {
      return 'Power through your workout!';
    } else {
      return 'Finish strong today!';
    }
  }
}
