import 'package:freezed_annotation/freezed_annotation.dart';

part 'user_stats.freezed.dart';
part 'user_stats.g.dart';

@freezed
class UserStats with _$UserStats {
  const factory UserStats({
    required int currentStreak,
    required int weeklyWorkouts,
    required int weeklyCalories,
    required int totalWorkouts,
    required double averageWorkoutDuration, // in minutes
    required List<WeeklyActivity> weeklyActivity,
    required DateTime lastWorkoutDate,
    @Default(false) bool hasWorkoutToday,
  }) = _UserStats;

  factory UserStats.fromJson(Map<String, dynamic> json) =>
      _$UserStatsFromJson(json);
}

@freezed
class WeeklyActivity with _$WeeklyActivity {
  const factory WeeklyActivity({
    required String day,
    required int minutes,
    required int calories,
    required DateTime date,
  }) = _WeeklyActivity;

  factory WeeklyActivity.fromJson(Map<String, dynamic> json) =>
      _$WeeklyActivityFromJson(json);
}

/// Extension for UserStats to provide formatted values
extension UserStatsExtension on UserStats {
  /// Get streak text with proper pluralization
  String get streakText {
    if (currentStreak == 0) return 'Start your streak!';
    if (currentStreak == 1) return '1 day streak!';
    return '$currentStreak day streak!';
  }

  /// Get weekly calories in imperial format (kcal)
  String get weeklyCaloriesText => '${weeklyCalories.toStringAsFixed(0)} kcal';

  /// Get average workout duration in minutes
  String get averageDurationText => '${averageWorkoutDuration.toInt()} min avg';

  /// Check if user worked out yesterday (for streak calculation)
  bool get workedOutYesterday {
    final yesterday = DateTime.now().subtract(const Duration(days: 1));
    return lastWorkoutDate.year == yesterday.year &&
           lastWorkoutDate.month == yesterday.month &&
           lastWorkoutDate.day == yesterday.day;
  }

  /// Get motivational message based on stats
  String get motivationalMessage {
    if (currentStreak >= 7) {
      return 'Amazing! You\'re on fire! 🔥';
    } else if (currentStreak >= 3) {
      return 'Great momentum! Keep it up! 💪';
    } else if (weeklyWorkouts >= 3) {
      return 'Solid week of training! 🎯';
    } else {
      return 'Every workout counts! 🌟';
    }
  }
}
