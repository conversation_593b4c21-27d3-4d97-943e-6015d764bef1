import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../../shared/widgets/glass_morphism_card.dart';
import '../../../../shared/widgets/animated_charts.dart';
import '../../../../core/animations/counting_animation.dart';
import '../../../../core/theme/color_palette.dart';
import '../../../../core/theme/typography.dart';
import '../../../../core/animations/animation_utils.dart';

class DashboardScreen extends StatefulWidget {
  const DashboardScreen({super.key});

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen>
    with TickerProviderStateMixin {
  late AnimationController _headerController;
  late AnimationController _cardsController;
  late List<Animation<double>> _cardAnimations;
  
  // Sample data
  final List<ChartData> weeklyData = [
    const ChartData(label: 'Mon', value: 45),
    const ChartData(label: 'Tue', value: 60),
    const ChartData(label: 'Wed', value: 30),
    const ChartData(label: 'Thu', value: 80),
    const ChartData(label: 'Fri', value: 55),
    const ChartData(label: 'Sat', value: 90),
    const ChartData(label: 'Sun', value: 70),
  ];

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _startAnimations();
  }

  void _setupAnimations() {
    _headerController = AnimationController(
      duration: AnimationUtils.normalDuration,
      vsync: this,
    );

    _cardsController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _cardAnimations = AnimationUtils.createStaggeredAnimations(
      controller: _cardsController,
      count: 6,
      staggerDelay: const Duration(milliseconds: 100),
    );
  }

  void _startAnimations() {
    _headerController.forward();
    Future.delayed(const Duration(milliseconds: 200), () {
      _cardsController.forward();
    });
  }

  @override
  void dispose() {
    _headerController.dispose();
    _cardsController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      backgroundColor: theme.colorScheme.background,
      body: CustomScrollView(
        physics: const BouncingScrollPhysics(),
        slivers: [
          _buildAnimatedAppBar(theme),
          SliverPadding(
            padding: const EdgeInsets.all(16),
            sliver: SliverList(
              delegate: SliverChildListDelegate([
                _buildStatsGrid(theme),
                const SizedBox(height: 24),
                _buildActivityChart(theme),
                const SizedBox(height: 24),
                _buildGoalsSection(theme),
                const SizedBox(height: 24),
                _buildRecentWorkouts(theme),
                const SizedBox(height: 100), // Bottom padding for navigation
              ]),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAnimatedAppBar(ThemeData theme) {
    return SliverAppBar(
      expandedHeight: 120,
      floating: true,
      pinned: true,
      backgroundColor: Colors.transparent,
      elevation: 0,
      systemOverlayStyle: SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: theme.brightness == Brightness.dark
            ? Brightness.light
            : Brightness.dark,
      ),
      flexibleSpace: FlexibleSpaceBar(
        background: AnimatedBuilder(
          animation: _headerController,
          builder: (context, child) {
            return Transform.translate(
              offset: Offset(0, 20 * (1 - _headerController.value)),
              child: Opacity(
                opacity: _headerController.value,
                child: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        AppColorPalette.primaryEnergyGradient.colors.first,
                        AppColorPalette.primaryEnergyGradient.colors.last,
                      ],
                    ),
                  ),
                  child: SafeArea(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          Text(
                            'Good Morning!',
                            style: AppTypography.displayNumbers(
                              fontSize: 28,
                              color: Colors.white,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'Ready to crush your goals today?',
                            style: theme.textTheme.bodyLarge?.copyWith(
                              color: Colors.white.withOpacity(0.9),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildStatsGrid(ThemeData theme) {
    final stats = [
      {'title': 'Steps Today', 'value': 8547.0, 'unit': 'steps', 'icon': Icons.directions_walk},
      {'title': 'Calories Burned', 'value': 342.0, 'unit': 'kcal', 'icon': Icons.local_fire_department},
      {'title': 'Active Minutes', 'value': 45.0, 'unit': 'min', 'icon': Icons.timer},
      {'title': 'Heart Rate', 'value': 72.0, 'unit': 'bpm', 'icon': Icons.favorite},
    ];

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
        childAspectRatio: 1.2,
      ),
      itemCount: stats.length,
      itemBuilder: (context, index) {
        final stat = stats[index];
        return AnimatedBuilder(
          animation: _cardAnimations[index],
          builder: (context, child) {
            return Transform.scale(
              scale: _cardAnimations[index].value,
              child: Opacity(
                opacity: _cardAnimations[index].value,
                child: AnimatedMetricCard(
                  title: stat['title'] as String,
                  value: stat['value'] as double,
                  unit: stat['unit'] as String,
                  icon: stat['icon'] as IconData,
                  color: _getStatColor(index),
                  onTap: () => _handleStatTap(index),
                ),
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildActivityChart(ThemeData theme) {
    return AnimatedBuilder(
      animation: _cardAnimations[4],
      builder: (context, child) {
        return Transform.scale(
          scale: _cardAnimations[4].value,
          child: Opacity(
            opacity: _cardAnimations[4].value,
            child: GlassMorphismCard(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Weekly Activity',
                        style: theme.textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      Icon(
                        Icons.trending_up,
                        color: AppColorPalette.successGreen,
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),
                  AnimatedBarChart(
                    data: weeklyData,
                    height: 150,
                    barColor: AppColorPalette.primaryOrange,
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildGoalsSection(ThemeData theme) {
    return AnimatedBuilder(
      animation: _cardAnimations[5],
      builder: (context, child) {
        return Transform.scale(
          scale: _cardAnimations[5].value,
          child: Opacity(
            opacity: _cardAnimations[5].value,
            child: GlassMorphismCard(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Today\'s Goals',
                    style: theme.textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 20),
                  AnimatedProgressCounter(
                    progress: 0.75,
                    label: 'Daily Steps',
                    progressColor: AppColorPalette.primaryOrange,
                  ),
                  const SizedBox(height: 16),
                  AnimatedProgressCounter(
                    progress: 0.60,
                    label: 'Calories Goal',
                    progressColor: AppColorPalette.accentBlue,
                  ),
                  const SizedBox(height: 16),
                  AnimatedProgressCounter(
                    progress: 0.90,
                    label: 'Active Time',
                    progressColor: AppColorPalette.successGreen,
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildRecentWorkouts(ThemeData theme) {
    final workouts = [
      {'name': 'Morning Run', 'duration': '30 min', 'calories': '245 kcal'},
      {'name': 'Strength Training', 'duration': '45 min', 'calories': '320 kcal'},
      {'name': 'Yoga Session', 'duration': '25 min', 'calories': '150 kcal'},
    ];

    return GlassMorphismCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Recent Workouts',
                style: theme.textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              TextButton(
                onPressed: () {},
                child: Text(
                  'View All',
                  style: TextStyle(color: AppColorPalette.primaryOrange),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          ...workouts.map((workout) => Padding(
            padding: const EdgeInsets.only(bottom: 12),
            child: Row(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: AppColorPalette.primaryOrange.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.fitness_center,
                    color: AppColorPalette.primaryOrange,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        workout['name']!,
                        style: theme.textTheme.bodyLarge?.copyWith(
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      Text(
                        '${workout['duration']} • ${workout['calories']}',
                        style: theme.textTheme.bodySmall,
                      ),
                    ],
                  ),
                ),
                Icon(
                  Icons.chevron_right,
                  color: theme.colorScheme.onSurface.withOpacity(0.5),
                ),
              ],
            ),
          )).toList(),
        ],
      ),
    );
  }

  Color _getStatColor(int index) {
    final colors = [
      AppColorPalette.primaryOrange,
      AppColorPalette.accentBlue,
      AppColorPalette.successGreen,
      Colors.red,
    ];
    return colors[index % colors.length];
  }

  void _handleStatTap(int index) {
    HapticFeedback.lightImpact();
    // Handle stat card tap
  }
}
