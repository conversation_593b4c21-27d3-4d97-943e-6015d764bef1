import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../shared/widgets/glass_morphism_card.dart';
import '../../../../shared/widgets/animated_charts.dart';
import '../../../../core/animations/counting_animation.dart';
import '../../../../core/theme/color_palette.dart';
import '../../../../core/theme/typography.dart';
import '../../../../core/animations/animation_utils.dart';
import '../../../auth/domain/providers/auth_provider.dart';
import '../../domain/providers/dashboard_provider.dart';
import '../widgets/today_workout_card.dart';
import '../widgets/user_greeting_header.dart';
import '../widgets/quick_stats_grid.dart';

class DashboardScreen extends ConsumerStatefulWidget {
  const DashboardScreen({super.key});

  @override
  ConsumerState<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends ConsumerState<DashboardScreen>
    with TickerProviderStateMixin {
  late AnimationController _headerController;
  late AnimationController _cardsController;
  late List<Animation<double>> _cardAnimations;
  
  // Sample data
  final List<ChartData> weeklyData = [
    const ChartData(label: 'Mon', value: 45),
    const ChartData(label: 'Tue', value: 60),
    const ChartData(label: 'Wed', value: 30),
    const ChartData(label: 'Thu', value: 80),
    const ChartData(label: 'Fri', value: 55),
    const ChartData(label: 'Sat', value: 90),
    const ChartData(label: 'Sun', value: 70),
  ];

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _startAnimations();
  }

  void _setupAnimations() {
    _headerController = AnimationController(
      duration: AnimationUtils.normalDuration,
      vsync: this,
    );

    _cardsController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _cardAnimations = AnimationUtils.createStaggeredAnimations(
      controller: _cardsController,
      count: 3,
      staggerDelay: const Duration(milliseconds: 100),
    );
  }

  void _startAnimations() {
    _headerController.forward();
    Future.delayed(const Duration(milliseconds: 200), () {
      _cardsController.forward();
    });
  }

  @override
  void dispose() {
    _headerController.dispose();
    _cardsController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.colorScheme.background,
      body: CustomScrollView(
        physics: const BouncingScrollPhysics(),
        slivers: [
          // Custom app bar with greeting
          SliverToBoxAdapter(
            child: SafeArea(
              child: UserGreetingHeader(),
            ),
          ),

          // Today's workout card
          SliverToBoxAdapter(
            child: AnimatedBuilder(
              animation: _headerController,
              builder: (context, child) {
                return Transform.translate(
                  offset: Offset(0, 20 * (1 - _headerController.value)),
                  child: Opacity(
                    opacity: _headerController.value,
                    child: const TodayWorkoutCard(),
                  ),
                );
              },
            ),
          ),

          const SliverToBoxAdapter(child: SizedBox(height: 32)),

          // Quick stats grid
          SliverToBoxAdapter(
            child: AnimatedBuilder(
              animation: _cardsController,
              builder: (context, child) {
                return Transform.translate(
                  offset: Offset(0, 30 * (1 - _cardsController.value)),
                  child: Opacity(
                    opacity: _cardsController.value,
                    child: const QuickStatsGrid(),
                  ),
                );
              },
            ),
          ),

          const SliverToBoxAdapter(child: SizedBox(height: 32)),

          // Activity chart and other sections
          SliverPadding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            sliver: SliverList(
              delegate: SliverChildListDelegate([
                _buildActivityChart(theme),
                const SizedBox(height: 24),
                _buildGoalsSection(theme),
                const SizedBox(height: 24),
                _buildRecentWorkouts(theme),
                const SizedBox(height: 100), // Bottom padding for navigation
              ]),
            ),
          ),
        ],
      ),
    );
  }





  Widget _buildActivityChart(ThemeData theme) {
    return AnimatedBuilder(
      animation: _cardAnimations[0],
      builder: (context, child) {
        return Transform.scale(
          scale: _cardAnimations[0].value,
          child: Opacity(
            opacity: _cardAnimations[0].value,
            child: GlassMorphismCard(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Weekly Activity',
                        style: theme.textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      Icon(
                        Icons.trending_up,
                        color: AppColorPalette.successGreen,
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),
                  AnimatedBarChart(
                    data: weeklyData,
                    height: 150,
                    barColor: AppColorPalette.primaryOrange,
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildGoalsSection(ThemeData theme) {
    return AnimatedBuilder(
      animation: _cardAnimations[1],
      builder: (context, child) {
        return Transform.scale(
          scale: _cardAnimations[1].value,
          child: Opacity(
            opacity: _cardAnimations[1].value,
            child: GlassMorphismCard(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Today\'s Goals',
                    style: theme.textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 20),
                  AnimatedProgressCounter(
                    progress: 0.75,
                    label: 'Daily Steps',
                    progressColor: AppColorPalette.primaryOrange,
                  ),
                  const SizedBox(height: 16),
                  AnimatedProgressCounter(
                    progress: 0.60,
                    label: 'Calories Goal',
                    progressColor: AppColorPalette.accentBlue,
                  ),
                  const SizedBox(height: 16),
                  AnimatedProgressCounter(
                    progress: 0.90,
                    label: 'Active Time',
                    progressColor: AppColorPalette.successGreen,
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildRecentWorkouts(ThemeData theme) {
    final workouts = [
      {'name': 'Morning Run', 'duration': '30 min', 'calories': '245 kcal'},
      {'name': 'Strength Training', 'duration': '45 min', 'calories': '320 kcal'},
      {'name': 'Yoga Session', 'duration': '25 min', 'calories': '150 kcal'},
    ];

    return GlassMorphismCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Recent Workouts',
                style: theme.textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              TextButton(
                onPressed: () {},
                child: Text(
                  'View All',
                  style: TextStyle(color: AppColorPalette.primaryOrange),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          ...workouts.map((workout) => Padding(
            padding: const EdgeInsets.only(bottom: 12),
            child: Row(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: AppColorPalette.primaryOrange.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.fitness_center,
                    color: AppColorPalette.primaryOrange,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        workout['name']!,
                        style: theme.textTheme.bodyLarge?.copyWith(
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      Text(
                        '${workout['duration']} • ${workout['calories']}',
                        style: theme.textTheme.bodySmall,
                      ),
                    ],
                  ),
                ),
                Icon(
                  Icons.chevron_right,
                  color: theme.colorScheme.onSurface.withOpacity(0.5),
                ),
              ],
            ),
          )).toList(),
        ],
      ),
    );
  }


}
