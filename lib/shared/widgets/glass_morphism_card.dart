import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../core/theme/color_palette.dart';
import '../../core/animations/animation_utils.dart';

/// Glass-morphism card with backdrop filter and gradient borders
class GlassMorphismCard extends StatefulWidget {
  final Widget child;
  final EdgeInsets? padding;
  final EdgeInsets? margin;
  final double? width;
  final double? height;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;
  final bool enableHoverEffect;
  final double borderRadius;
  final double blurIntensity;
  final List<Color>? gradientColors;
  final double elevation;
  final bool enableHapticFeedback;

  const GlassMorphismCard({
    super.key,
    required this.child,
    this.padding,
    this.margin,
    this.width,
    this.height,
    this.onTap,
    this.onLongPress,
    this.enableHoverEffect = true,
    this.borderRadius = 16.0,
    this.blurIntensity = 10.0,
    this.gradientColors,
    this.elevation = 8.0,
    this.enableHapticFeedback = true,
  });

  @override
  State<GlassMorphismCard> createState() => _GlassMorphismCardState();
}

class _GlassMorphismCardState extends State<GlassMorphismCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  late Animation<double> _elevationAnimation;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: AnimationUtils.fastDuration,
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.97,
    ).animate(
      CurvedAnimation(
        parent: _controller,
        curve: FitnessAnimationCurves.cardHover,
      ),
    );

    _elevationAnimation = Tween<double>(
      begin: widget.elevation,
      end: widget.elevation * 1.5,
    ).animate(
      CurvedAnimation(
        parent: _controller,
        curve: FitnessAnimationCurves.cardHover,
      ),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _handleTapDown(TapDownDetails details) {
    if (widget.onTap != null) {
      setState(() => _isPressed = true);
      _controller.forward();
      if (widget.enableHapticFeedback) {
        HapticFeedback.lightImpact();
      }
    }
  }

  void _handleTapUp(TapUpDetails details) {
    if (_isPressed) {
      setState(() => _isPressed = false);
      _controller.reverse();
      widget.onTap?.call();
    }
  }

  void _handleTapCancel() {
    if (_isPressed) {
      setState(() => _isPressed = false);
      _controller.reverse();
    }
  }

  void _handleLongPress() {
    if (widget.enableHapticFeedback) {
      HapticFeedback.mediumImpact();
    }
    widget.onLongPress?.call();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    
    final gradientColors = widget.gradientColors ?? 
        (isDark 
            ? AppColorPalette.darkGlassMorphismGradient.colors
            : AppColorPalette.glassMorphismGradient.colors);

    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Container(
          margin: widget.margin,
          width: widget.width,
          height: widget.height,
          child: Transform.scale(
            scale: _scaleAnimation.value,
            child: GestureDetector(
              onTapDown: _handleTapDown,
              onTapUp: _handleTapUp,
              onTapCancel: _handleTapCancel,
              onLongPress: widget.onLongPress != null ? _handleLongPress : null,
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(widget.borderRadius),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(isDark ? 0.3 : 0.1),
                      blurRadius: _elevationAnimation.value,
                      offset: Offset(0, _elevationAnimation.value / 2),
                    ),
                    if (isDark)
                      BoxShadow(
                        color: Colors.white.withOpacity(0.05),
                        blurRadius: 1,
                        offset: const Offset(0, 1),
                      ),
                  ],
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(widget.borderRadius),
                  child: BackdropFilter(
                    filter: ImageFilter.blur(
                      sigmaX: widget.blurIntensity,
                      sigmaY: widget.blurIntensity,
                    ),
                    child: Container(
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: gradientColors,
                        ),
                        borderRadius: BorderRadius.circular(widget.borderRadius),
                        border: Border.all(
                          color: Colors.white.withOpacity(isDark ? 0.1 : 0.2),
                          width: 1,
                        ),
                      ),
                      child: Padding(
                        padding: widget.padding ?? const EdgeInsets.all(16),
                        child: widget.child,
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

/// Floating glass-morphism card with enhanced effects
class FloatingGlassCard extends StatefulWidget {
  final Widget child;
  final EdgeInsets? padding;
  final EdgeInsets? margin;
  final VoidCallback? onTap;
  final double borderRadius;
  final bool enableParallax;
  final double parallaxIntensity;

  const FloatingGlassCard({
    super.key,
    required this.child,
    this.padding,
    this.margin,
    this.onTap,
    this.borderRadius = 20.0,
    this.enableParallax = true,
    this.parallaxIntensity = 0.02,
  });

  @override
  State<FloatingGlassCard> createState() => _FloatingGlassCardState();
}

class _FloatingGlassCardState extends State<FloatingGlassCard>
    with TickerProviderStateMixin {
  late AnimationController _floatingController;
  late AnimationController _hoverController;
  late Animation<double> _floatingAnimation;
  late Animation<double> _scaleAnimation;
  Offset _parallaxOffset = Offset.zero;

  @override
  void initState() {
    super.initState();
    
    _floatingController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );

    _hoverController = AnimationController(
      duration: AnimationUtils.normalDuration,
      vsync: this,
    );

    _floatingAnimation = Tween<double>(
      begin: 0.0,
      end: 8.0,
    ).animate(
      CurvedAnimation(
        parent: _floatingController,
        curve: Curves.easeInOut,
      ),
    );

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.05,
    ).animate(
      CurvedAnimation(
        parent: _hoverController,
        curve: FitnessAnimationCurves.cardHover,
      ),
    );

    _floatingController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _floatingController.dispose();
    _hoverController.dispose();
    super.dispose();
  }

  void _handlePanUpdate(DragUpdateDetails details) {
    if (widget.enableParallax) {
      setState(() {
        _parallaxOffset = details.localPosition * widget.parallaxIntensity;
      });
    }
  }

  void _handlePanEnd(DragEndDetails details) {
    if (widget.enableParallax) {
      setState(() {
        _parallaxOffset = Offset.zero;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: Listenable.merge([_floatingController, _hoverController]),
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(0, _floatingAnimation.value) + _parallaxOffset,
          child: Transform.scale(
            scale: _scaleAnimation.value,
            child: MouseRegion(
              onEnter: (_) => _hoverController.forward(),
              onExit: (_) => _hoverController.reverse(),
              child: GestureDetector(
                onPanUpdate: _handlePanUpdate,
                onPanEnd: _handlePanEnd,
                onTap: widget.onTap,
                child: GlassMorphismCard(
                  padding: widget.padding,
                  margin: widget.margin,
                  borderRadius: widget.borderRadius,
                  elevation: 12.0,
                  blurIntensity: 15.0,
                  child: widget.child,
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

/// Glass-morphism navigation bar
class GlassNavigationBar extends StatelessWidget {
  final List<GlassNavigationItem> items;
  final int currentIndex;
  final ValueChanged<int>? onTap;
  final double height;
  final EdgeInsets? margin;

  const GlassNavigationBar({
    super.key,
    required this.items,
    required this.currentIndex,
    this.onTap,
    this.height = 80.0,
    this.margin,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      margin: margin ?? const EdgeInsets.all(16),
      height: height,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(25),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 20, sigmaY: 20),
          child: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: theme.brightness == Brightness.dark
                    ? AppColorPalette.darkGlassMorphismGradient.colors
                    : AppColorPalette.glassMorphismGradient.colors,
              ),
              borderRadius: BorderRadius.circular(25),
              border: Border.all(
                color: Colors.white.withOpacity(0.1),
                width: 1,
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: items.asMap().entries.map((entry) {
                final index = entry.key;
                final item = entry.value;
                final isSelected = index == currentIndex;

                return GestureDetector(
                  onTap: () => onTap?.call(index),
                  child: AnimatedContainer(
                    duration: AnimationUtils.normalDuration,
                    curve: FitnessAnimationCurves.springCurve,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12,
                    ),
                    decoration: BoxDecoration(
                      color: isSelected
                          ? theme.colorScheme.primary.withOpacity(0.2)
                          : Colors.transparent,
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          isSelected ? item.activeIcon : item.icon,
                          color: isSelected
                              ? theme.colorScheme.primary
                              : theme.colorScheme.onSurface.withOpacity(0.6),
                          size: 24,
                        ),
                        const SizedBox(height: 4),
                        Text(
                          item.label,
                          style: theme.textTheme.labelSmall?.copyWith(
                            color: isSelected
                                ? theme.colorScheme.primary
                                : theme.colorScheme.onSurface.withOpacity(0.6),
                            fontWeight: isSelected
                                ? FontWeight.w600
                                : FontWeight.w400,
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              }).toList(),
            ),
          ),
        ),
      ),
    );
  }
}

/// Navigation item for glass navigation bar
class GlassNavigationItem {
  final IconData icon;
  final IconData activeIcon;
  final String label;

  const GlassNavigationItem({
    required this.icon,
    required this.activeIcon,
    required this.label,
  });
}
