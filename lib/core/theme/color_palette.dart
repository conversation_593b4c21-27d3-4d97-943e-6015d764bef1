import 'package:flutter/material.dart';

/// Comprehensive color palette for the fitness app
/// Supports both light and dark themes with semantic color tokens
class AppColorPalette {
  // Primary brand colors - Orange gradient system for energy/achievement
  static const Color primaryOrange = Color(0xFFFF6B35);
  static const Color primaryOrangeLight = Color(0xFFFF8E53);
  static const Color primaryOrangeDark = Color(0xFFE55A2B);

  // Complementary blue accent for data visualization
  static const Color accentBlue = Color(0xFF4A90E2);
  static const Color accentBlueLight = Color(0xFF6BA3E8);
  static const Color accentBlueDark = Color(0xFF3A7BC8);

  // Legacy blue color for backward compatibility
  static const Color primaryBlue = accentBlue;

  // Success green for goal completions
  static const Color successGreen = Color(0xFF00C851);
  static const Color successGreenLight = Color(0xFF2DD36F);
  static const Color successGreenDark = Color(0xFF00A844);

  // Near-black background with warm tint
  static const Color darkBackground = Color(0xFF0A0A0B);

  // Dark theme elevation levels
  static const Color elevation1 = Color(0xFF1A1A1B);
  static const Color elevation2 = Color(0xFF2A2A2B);
  static const Color elevation3 = Color(0xFF3A3A3B);
  static const Color elevation4 = Color(0xFF4A4A4B);

  // Neutral colors
  static const Color white = Color(0xFFFFFFFF);
  static const Color black = Color(0xFF000000);
  static const Color grey50 = Color(0xFFFAFAFA);
  static const Color grey100 = Color(0xFFF5F5F5);
  static const Color grey200 = Color(0xFFE5E5E5);
  static const Color grey300 = Color(0xFFD4D4D4);
  static const Color grey400 = Color(0xFFA3A3A3);
  static const Color grey500 = Color(0xFF737373);
  static const Color grey600 = Color(0xFF525252);
  static const Color grey700 = Color(0xFF404040);
  static const Color grey800 = Color(0xFF262626);
  static const Color grey900 = Color(0xFF171717);

  // Semantic colors
  static const Color success = successGreen;
  static const Color warning = Color(0xFFF59E0B);
  static const Color error = Color(0xFFEF4444);
  static const Color info = accentBlue;
  
  // Fitness-specific colors
  static const Color cardio = Color(0xFFFF6B6B);
  static const Color strength = Color(0xFF4ECDC4);
  static const Color flexibility = Color(0xFFFFE66D);
  static const Color endurance = Color(0xFF95E1D3);
  
  // Light theme colors
  static const ColorScheme lightColorScheme = ColorScheme(
    brightness: Brightness.light,
    primary: primaryOrange,
    onPrimary: white,
    primaryContainer: Color(0xFFFFE4D6),
    onPrimaryContainer: Color(0xFF7C2D12),
    secondary: accentBlue,
    onSecondary: white,
    secondaryContainer: Color(0xFFE3F2FD),
    onSecondaryContainer: Color(0xFF1565C0),
    tertiary: successGreen,
    onTertiary: white,
    tertiaryContainer: Color(0xFFE8F5E8),
    onTertiaryContainer: Color(0xFF2E7D32),
    error: error,
    onError: white,
    errorContainer: Color(0xFFFEE2E2),
    onErrorContainer: Color(0xFF7F1D1D),
    background: white,
    onBackground: grey900,
    surface: white,
    onSurface: grey900,
    surfaceVariant: grey100,
    onSurfaceVariant: grey700,
    outline: grey300,
    outlineVariant: grey200,
    shadow: black,
    scrim: black,
    inverseSurface: grey900,
    onInverseSurface: grey100,
    inversePrimary: primaryOrangeLight,
  );
  
  // Dark theme colors with near-black background and elevation levels
  static const ColorScheme darkColorScheme = ColorScheme(
    brightness: Brightness.dark,
    primary: primaryOrangeLight,
    onPrimary: darkBackground,
    primaryContainer: primaryOrangeDark,
    onPrimaryContainer: Color(0xFFFFE4D6),
    secondary: accentBlueLight,
    onSecondary: darkBackground,
    secondaryContainer: accentBlueDark,
    onSecondaryContainer: Color(0xFFE3F2FD),
    tertiary: successGreenLight,
    onTertiary: darkBackground,
    tertiaryContainer: successGreenDark,
    onTertiaryContainer: Color(0xFFE8F5E8),
    error: Color(0xFFFCA5A5),
    onError: Color(0xFF7F1D1D),
    errorContainer: Color(0xFFDC2626),
    onErrorContainer: Color(0xFFFEE2E2),
    background: darkBackground,
    onBackground: grey100,
    surface: elevation1,
    onSurface: grey100,
    surfaceVariant: elevation2,
    onSurfaceVariant: grey300,
    outline: grey600,
    outlineVariant: grey700,
    shadow: black,
    scrim: black,
    inverseSurface: grey100,
    onInverseSurface: darkBackground,
    inversePrimary: primaryOrange,
  );
  
  // Enhanced gradient definitions for energy/achievement states
  static const LinearGradient primaryGradient = LinearGradient(
    colors: [primaryOrange, primaryOrangeLight],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient primaryEnergyGradient = LinearGradient(
    colors: [primaryOrange, primaryOrangeLight],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient achievementGradient = LinearGradient(
    colors: [primaryOrangeLight, Color(0xFFFFB088)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient dataVisualizationGradient = LinearGradient(
    colors: [accentBlue, accentBlueLight],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient successGradient = LinearGradient(
    colors: [successGreen, successGreenLight],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient secondaryGradient = LinearGradient(
    colors: [accentBlue, accentBlueLight],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  // Glass-morphism gradient for cards
  static const LinearGradient glassMorphismGradient = LinearGradient(
    colors: [
      Color(0x20FFFFFF),
      Color(0x10FFFFFF),
    ],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  // Dark glass-morphism gradient
  static const LinearGradient darkGlassMorphismGradient = LinearGradient(
    colors: [
      Color(0x20FFFFFF),
      Color(0x05FFFFFF),
    ],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  // Surface colors for cards and containers with elevation levels
  static Color lightSurface = grey50;
  static Color lightSurfaceVariant = grey100;
  static Color darkSurface = elevation1;
  static Color darkSurfaceVariant = elevation2;
  static Color darkSurfaceElevated = elevation3;
  static Color darkSurfaceHighest = elevation4;

  // Text colors with WCAG AAA contrast ratios
  static Color lightTextPrimary = grey900;
  static Color lightTextSecondary = grey600;
  static Color lightTextTertiary = grey500;
  static Color darkTextPrimary = grey100;
  static Color darkTextSecondary = grey300;
  static Color darkTextTertiary = grey400;

  // High contrast variants for accessibility
  static Color highContrastLight = black;
  static Color highContrastDark = white;
  static Color highContrastBackground = Color(0xFFFFFFF0);
  static Color highContrastSurface = Color(0xFF000010);
}
